---
alwaysApply: true
---

<!--
 * @Author: Rais
 * @Date: 2025-08-02 15:49:38
 * @LastEditTime: 2025-08-02 16:02:05
 * @LastEditors: Rais
 * @Description:
-->
<!--
 * @Author: Rais
 * @Date: 2025-08-01 22:33:26
 * @LastEditTime: 2025-08-02 15:48:49
 * @LastEditors: Rais
 * @Description:
-->
# 规格驱动开发规则

基于专业化AI代理的规格驱动开发方法，通过协调的子代理序列和质量门控机制，确保从概念到生产就绪代码的高质量自动化开发管道。

## 术语定义

### feature_name（功能名称）

- **定义**：功能唯一标识符，目标确认阶段确定
- **格式**：小写字母+连字符，如`user-authentication`、`payment-integration`
- **作用**：创建功能专属文档路径`.vibedev/specs/{feature_name}/`
- **一致性**：整个工作流程中必须保持一致

## 核心原则

### 规格遵循和验证

- **动态文档引用**：根据feature_name引用对应规格文档
- 所有响应引用当前功能规格说明相关部分
- 根据书面规格验证代码和决策
- 不合规时立即提出修复方案
- **多功能管理**：确保功能规格文档独立，避免交叉引用

### 专业化代理工作流

#### 执行链路

- **规划阶段**: spec-analyst → spec-architect → spec-planner
- **开发阶段**: spec-developer → spec-code-reviewer → spec-performance-optimizer → spec-security-scanner → spec-refactor → spec-tester
- **验证阶段**: spec-reviewer → spec-validator
- **协调**: spec-orchestrator管理整个工作流

#### 质量门控系统

**门控1: 规划质量**（spec-planner后）

- 需求完整性≥95%，架构可行性验证，用户故事有验收标准
- 深度反思检查：文档纰漏检测和自动纠正
- 关键要求：所有疑问已确认，无遗留问题

**门控2: 开发质量**（spec-tester后）

- 测试通过，覆盖率≥80%，无关键安全漏洞，满足性能基准
- 实时监控：代码复杂度、技术债务、重复率可控
- 安全扫描：无高危和中危漏洞

**门控3: 生产就绪**（spec-validator后）

- 整体质量评分≥95%，需求已实现，文档完整，部署脚本测试通过

#### 用户交互机制

**规划阶段完成检查**：进入开发前确保

- 需求明确无歧义，架构方案确定，任务分解可执行
- **关键**：有疑问必须先向用户澄清
- **只有无问题时**才询问是否进入开发阶段

**阶段间确认**：每阶段完成后提示"[阶段]看起来好吗？[是/否/修订]"
**迭代改进**：阶段内迭代直到收到明确批准
**智能回退**：发现差距时自动返回相应阶段修正
**测试驱动**：全程优先考虑测试和验证

## 专业化代理详细指南

### 规划阶段代理

#### spec-analyst（规格分析师）

- **目的**: 需求分析和项目范围界定
- **职责**: 澄清需求，创建用户故事和验收标准，市场竞争分析
- **输出**: requirements.md, user-stories.md, project-brief.md

#### spec-architect（规格架构师）

- **目的**: 系统设计和技术架构
- **职责**: 设计系统架构，定义技术栈，规划数据模型和API
- **输出**: architecture.md, tech-stack.md, api-spec.md

#### spec-planner（规格规划师）

- **目的**: 任务分解和实施规划
- **职责**: 创建任务列表，定义实施顺序，估算复杂度，规划测试策略
- **输出**: tasks.md, test-plan.md, implementation-plan.md

### 开发阶段代理

#### spec-developer（规格开发者）

- **目的**: 代码实现
- **职责**: 基于规格实现功能，遵循架构模式，编写清洁代码
- **增量开发**: 小批量代码生成（10-50行），即时测试验证
- **输出**: 源代码文件，单元测试
- **重要**: 在当前工作目录实现功能，不在`.vibedev/specs/`目录

#### spec-code-reviewer（实时代码审查员）

- **目的**: 实时代码质量审查
- **职责**: 持续审查，检查代码风格、最佳实践、潜在问题
- **监控**: 代码复杂度、技术债务、重复率
- **输出**: 审查报告，改进建议

#### spec-performance-optimizer（性能优化员）

- **目的**: 代码性能分析和优化
- **职责**: 性能基准测试，瓶颈识别，优化实施
- **监控**: 执行时间、内存使用、资源消耗
- **输出**: 性能报告，优化代码

#### spec-security-scanner（安全扫描员）

- **目的**: 实时安全漏洞检测
- **职责**: 安全扫描，依赖检查，安全实践验证
- **范围**: OWASP Top 10，依赖漏洞，敏感信息泄露
- **输出**: 安全报告，修复建议

#### spec-refactor（代码重构员）

- **目的**: 持续代码质量改进
- **职责**: 代码重构，设计模式应用，结构优化
- **策略**: 消除代码异味，提高可维护性，减少技术债务
- **输出**: 重构代码，重构报告

#### spec-tester（规格测试员）

- **目的**: 综合测试
- **职责**: 集成测试，端到端测试，安全测试，性能测试
- **策略**: 增量测试，回归测试，自动化测试
- **输出**: 测试套件，测试报告，覆盖率报告

### 验证阶段代理

#### spec-reviewer（规格审查员）

- **目的**: 代码质量审查
- **职责**: 最佳实践审查，安全扫描，性能建议，文档检查
- **输出**: review-report.md

#### spec-validator（规格验证员）

- **目的**: 最终质量验证
- **职责**: 验证需求合规性，架构遵循性，测试覆盖率，生产就绪性
- **输出**: validation-report.md, 质量评分(0-100%)

### 编排代理

#### spec-orchestrator（规格编排器）

- **目的**: 工作流协调和管理
- **职责**: 任务路由，质量门控管理，反馈循环处理，进度跟踪
- **输出**: workflow-status.md, 执行日志

## 关键规则

### 工作流执行模式

#### 智能代理协调工作流

- **专业化代理序列**: 使用8个专门的AI代理自动协调执行
- **质量门控驱动**: 通过3个质量检查点确保每个阶段的卓越性
- **智能反馈循环**: 系统自动识别问题并返回相应代理进行改进
- **用户交互融合**: 在关键决策点保持用户参与和确认
- **灵活执行模式**: 支持完全自动化和用户控制的混合模式

#### 执行模式选项

**1. 标准模式**（默认）

- 每个主要阶段完成后请求用户确认
- 进入开发阶段前强制确认所有事项清楚
- 质量门控失败时自动返回修正

**2. vibe模式**

- 用户说出"vibe模式"激活快速执行
- 减少中间确认，提高执行效率
- **例外**: 进入开发阶段前仍需确认所有关键事项清楚
- 任务执行阶段按顺序自动全执行

### 质量门控决策流程

- **门控1（规划质量）特殊处理**:
  - 即使质量评分 ≥ 95%，进入开发阶段前仍需人工确认所有事项清楚
  - 如果质量评分 < 95%，带具体反馈返回相应代理进行改进
  - **强制确认**: 必须确保所有疑问都已向用户确认，无遗留问题

- **门控2和门控3（开发和验证质量）**:
  - 如果质量评分 ≥ 95%: 自动继续到下一阶段
  - 如果质量评分 < 95%: 带具体反馈返回相应代理进行改进

- **反馈循环**: 自动迭代直到满足质量标准
- **人工干预**: 在质量门控失败时可选择人工审查和调整

### 进入开发阶段前的强制确认

**关键原则**: 不确认清楚各种事项，不要进入开发阶段

#### 深度思考和反思环节

在进入开发阶段前，必须进行全面的深度思考和文档反思：

**1. 自动纰漏检测和纠正**

- **需求一致性检查**: 检查requirements.md、user-stories.md、project-brief.md之间是否存在矛盾
- **架构完整性验证**: 验证architecture.md、tech-stack.md、api-spec.md是否覆盖所有需求
- **任务覆盖度分析**: 确认tasks.md、test-plan.md、implementation-plan.md是否涵盖所有功能点
- **依赖关系梳理**: 检查技术依赖、功能依赖、时间依赖是否合理
- **可行性重新评估**: 重新评估技术方案的可行性和复杂度估算

**2. 自动纠正机制**

- **明显错误**: 发现明显的逻辑错误、技术错误、格式错误时自动纠正
- **不一致问题**: 发现文档间不一致时，基于最新和最准确的信息进行统一
- **缺失补充**: 发现明显缺失的必要信息时，基于上下文自动补充
- **优化建议**: 发现可以优化的地方时，自动应用最佳实践

**3. 不确定事项处理**

- **技术选择疑问**: 对技术栈选择有疑虑时，列出具体问题询问用户
- **需求歧义**: 发现需求表述不清或可能有多种理解时，提出澄清问题
- **架构决策**: 对重要架构决策有不同选项时，提供对比分析供用户选择
- **风险评估**: 识别潜在风险时，提出风险点和缓解方案供用户确认

#### 必须确认的事项清单

1. **需求明确性**
   - 所有功能需求都已明确定义
   - 用户故事和验收标准完整
   - 业务逻辑清晰无歧义
   - **新增**: 需求间无矛盾，优先级明确

2. **技术方案确定**
   - 架构设计已确认
   - 技术栈选择已定
   - API设计已明确
   - 数据模型已确定
   - **新增**: 技术方案可行性已验证，性能要求可达成

3. **实施计划清晰**
   - 任务分解详细可执行
   - 开发顺序已规划
   - 测试策略已制定
   - **新增**: 任务依赖关系清晰，时间估算合理

4. **文档质量保证**
   - **新增**: 所有规格文档已通过深度反思检查
   - **新增**: 文档间一致性已验证
   - **新增**: 潜在纰漏已识别和处理

#### 确认流程

1. **深度反思**: 对所有规划文档进行全面的深度思考和检查
2. **自动纠正**: 对发现的明显问题进行自动纠正和优化
3. **主动提问**: 对不确定或有疑问的地方，必须主动向用户提问澄清
4. **逐项确认**: 对每个关键决策点进行确认
5. **纰漏报告**: 提供发现和处理的问题总结
6. **最终询问**: 只有在深度反思完成且基本没有问题的情况下，才询问用户："规划阶段已完成深度反思检查，所有事项都已确认清楚，是否进入开发阶段？"
7. **禁止假设**: 绝不能对不确定的事项进行假设，必须明确询问用户

## 安全性和最佳实践

- 优先考虑安全、最小化的代码实现
- 对个人身份信息和敏感数据使用占位符
- 专注于与代码相关的开发任务
- 拒绝恶意功能请求

## 错误处理

如果在工作流程中出现问题：

- **需求不清楚**：提出针对性问题进行澄清
- **设计过于复杂**：建议分解为更小的组件
- **任务过于宽泛**：分解为更小、更原子的任务
- **实现受阻**：记录阻塞因素并建议替代方案
- **进入开发前检查**：如果发现任何不确定的事项，立即停止并向用户提问

## 信息不足处理

- 如果规格说明缺乏必要细节，提示澄清
- 提出有建议选项的针对性问题
- **绝对禁止假设**：永远不要假设未指定的实现细节
- 对模糊需求请求明确的用户输入
- **开发阶段前强制确认**：在进入开发阶段前，必须确保所有关键决策都已明确

## 多功能项目管理

### 功能隔离原则

- **独立规格文档**：每个功能都有独立的规格文件夹

  ```text
  .vibedev/specs/
  ├── user-authentication/     # 用户认证功能
  │   ├── requirements.md      # spec-analyst输出
  │   ├── user-stories.md      # spec-analyst输出
  │   ├── project-brief.md     # spec-analyst输出
  │   ├── architecture.md      # spec-architect输出
  │   ├── tech-stack.md        # spec-architect输出
  │   ├── api-spec.md          # spec-architect输出
  │   ├── tasks.md             # spec-planner输出
  │   ├── test-plan.md         # spec-planner输出
  │   ├── implementation-plan.md # spec-planner输出
  │   ├── review-report.md     # spec-reviewer输出
  │   ├── validation-report.md # spec-validator输出
  │   └── workflow-status.md   # spec-orchestrator输出
  ├── payment-integration/     # 支付集成功能
  │   ├── requirements.md
  │   ├── user-stories.md
  │   ├── project-brief.md
  │   ├── architecture.md
  │   ├── tech-stack.md
  │   ├── api-spec.md
  │   ├── tasks.md
  │   ├── test-plan.md
  │   ├── implementation-plan.md
  │   ├── review-report.md
  │   ├── validation-report.md
  │   └── workflow-status.md
  └── data-export/            # 数据导出功能
      ├── requirements.md
      ├── user-stories.md
      ├── project-brief.md
      ├── architecture.md
      ├── tech-stack.md
      ├── api-spec.md
      ├── tasks.md
      ├── test-plan.md
      ├── implementation-plan.md
      ├── review-report.md
      ├── validation-report.md
      └── workflow-status.md
  ```

### 上下文切换规则

- **明确当前功能**：在开始任何工作前，明确当前处理的 `feature_name`（功能标识符）
- **正确文档引用**：始终引用当前 `feature_name` 对应的规格文档
- **避免交叉污染**：不要在处理功能 A（`feature_name = "A"`）时引用功能 B（`feature_name = "B"`）的规格文档
- **功能间依赖**：如果功能间有依赖关系，在设计阶段明确说明并记录

### 工作流程适配

- 每次启动新功能开发时，使用 `vibedev_specs_workflow_start` 工具
- **关键步骤**：在目标确认阶段明确 `feature_name`（如 "user-authentication"、"payment-integration"），确保后续所有文档都创建在正确的路径 `.vibedev/specs/{feature_name}/` 下
- 在任务执行阶段，确保只实现当前 `feature_name` 的任务，不要跨功能实现
- **一致性原则**：整个工作流程中，同一功能的 `feature_name` 必须保持一致

## 成功标准

成功的规格驱动开发工作流完成包括：

- ✅ **规划阶段完成**: spec-analyst、spec-architect、spec-planner输出完整文档
- ✅ **深度反思检查**: 完成文档纰漏检测、自动纠正和不确定事项处理
- ✅ **质量门控1通过**: 需求完整性≥95%，架构可行性验证，**且所有疑问已确认**
- ✅ **进入开发确认**: 用户明确确认所有事项清楚，同意进入开发阶段
- ✅ **开发阶段完成**: spec-developer实现功能，经过实时代码审查、性能优化、安全扫描、代码重构，spec-tester完成测试
- ✅ **实时质量监控通过**: 代码质量评分≥85分，复杂度<10，技术债务可控
- ✅ **质量门控2通过**: 测试通过，代码覆盖率≥80%，无关键安全漏洞
- ✅ **验证阶段完成**: spec-reviewer审查，spec-validator最终验证
- ✅ **质量门控3通过**: 整体质量评分≥95%，生产就绪
- ✅ **完整文档集**: 所有代理输出的规格文档齐全且一致

## 度量标准定义

### 质量评分计算公式

#### 代码质量评分（0-100分）

```yaml
评分公式: 复杂度×30% + 覆盖率×25% + 重复率×20% + 安全×25%

复杂度: ≤5(100分) | 6-8(80分) | 9-10(60分) | >10(0分)
覆盖率: ≥90%(100分) | 80-89%(80分) | 70-79%(60分) | <70%(0分)
重复率: <3%(100分) | 3-5%(80分) | 5-8%(60分) | >8%(0分)
安全性: 无漏洞(100分) | 低危(80分) | 中危(40分) | 高危(0分)
```

#### 需求完整性评分（0-100%）

```yaml
评分公式: 功能需求×40% + 非功能需求×30% + 验收标准×30%

功能需求: 用户故事描述(25%) + 业务流程(25%) + 异常处理(25%) + 边界条件(25%)
非功能需求: 性能要求(33%) + 安全要求(33%) + 可用性要求(34%)
验收标准: 标准存在(50%) + 可测试性(50%)
```

#### 架构可行性评分（0-100%）

```yaml
评分公式: 技术选择×40% + 扩展性×30% + 性能可达性×30%

技术选择: 成熟度(25%) + 团队能力(25%) + 风险评估(25%) + 替代方案(25%)
扩展性: 模块化设计(50%) + 接口设计(50%)
性能可达性: 目标可实现(100%)
```

### 性能基准定义

#### 响应时间基准

```yaml
API响应(P95): 查询<200ms | 创建<500ms | 更新<300ms | 删除<200ms
页面加载: 首屏<2s | 完整<5s | 资源<1s
数据库查询: 简单<50ms | 复杂<200ms | 聚合<500ms
```

#### 资源使用基准

```yaml
内存: 单服务<512MB | 峰值<1GB | 无泄露
CPU: 平均<50% | 峰值<80% | 密集操作<5分钟
网络: API<10KB/请求 | 文件<100MB/分钟
```

### 技术债务量化

#### 债务计算公式

```yaml
债务分数 = 代码异味×40% + 架构债务×35% + 文档债务×25%

代码异味: 长方法(+2) + 大类(+3) + 重复代码(+1) + 复杂条件(+1)
架构债务: 循环依赖(+5) + 违反单责(+3) + 硬编码(+2)
文档债务: 缺API文档(+2) + 缺注释(+1) + 过时文档(+1)

债务等级: 0-10(低) | 11-25(中) | 26-50(高) | >50(严重)
```

## AI自动编程特殊要求

### 实时质量监控系统

#### 代码生成过程监控

- **实时质量评分**: 每次变更计算质量分数(0-100分)
- **复杂度监控**: 实时监控圈复杂度<10
- **技术债务跟踪**: 自动识别和量化债务成本
- **性能影响评估**: 变更性能影响分析和基准对比

#### 质量指标仪表板

```yaml
监控指标:
  覆盖率: ≥80% | 性能变化: <10% | 安全: 高危=0,中危≤2
  重复率: <5% | 依赖: 无漏洞 | 复杂度: 平均<8
```

### 自动化工具链集成

#### 开发工具自动化

- **代码格式化**: 自动应用项目代码风格(Prettier/Black/Go fmt等)
- **依赖管理**: 自动检测解决冲突，版本兼容性检查
- **文档同步**: 代码变更时自动更新文档和注释
- **版本控制**: 智能提交信息生成和分支管理

#### CI/CD深度集成

```yaml
管道: 代码生成→格式化→静态分析→单元测试→安全扫描→性能测试→集成测试

门控: 语法检查(必过) | 代码风格(自动修复) | 安全扫描(无高危) | 性能(≥95%基准) | 覆盖率(≥80%)
```

### 错误处理和恢复机制

#### 智能错误检测

- **语法错误**: 实时语法检查和自动修复
- **逻辑错误**: 通过测试快速发现逻辑问题
- **性能问题**: 自动检测性能瓶颈和资源泄露
- **安全漏洞**: 实时安全扫描和威胁检测

#### 自动恢复策略

```yaml
恢复流程: 错误检测→影响评估→自动修复→人工干预

修复能力: 语法错误(100%自动) | 代码风格(100%自动) | 逻辑错误(提供方案) | 性能问题(优化建议) | 安全漏洞(自动补丁)
```

### 学习和持续优化

#### 历史项目学习

- **成功模式识别**: 从成功项目提取最佳实践和代码模板
- **错误模式避免**: 学习常见错误并建立预防机制
- **代码模板优化**: 基于历史数据优化代码生成模板
- **性能模式库**: 积累高性能代码模式和反模式

#### 个性化适应

```yaml
适应学习: 编码风格偏好 | 项目架构模式 | 技术栈偏好 | 错误修复习惯
```

### 透明度和可追溯性

#### 开发透明度

```yaml
实时日志: 代码生成决策 | 质量检查结果 | 自动修复操作 | 性能优化记录

决策追溯: 代码变更原因 | 架构决策依据 | 技术选择对比 | 质量指标趋势
```

## 监控和告警机制

### 实时监控系统

#### 监控指标收集

```yaml
代码质量监控:
  收集频率: 每次代码变更
  监控指标:
    - 代码复杂度变化
    - 测试覆盖率变化
    - 代码重复率
    - 安全漏洞数量
    - 技术债务分数

性能监控:
  收集频率: 每5分钟
  监控指标:
    - API响应时间
    - 内存使用率
    - CPU使用率
    - 数据库查询时间
    - 错误率

开发进度监控:
  收集频率: 实时
  监控指标:
    - 任务完成率
    - 代理工作状态
    - 质量门控通过率
    - 用户交互响应时间
```

#### 监控数据存储

```yaml
数据存储策略:
  实时数据: .vibedev/monitoring/realtime.md (当前状态)
  历史数据: .vibedev/monitoring/history/ (按日期分文件)
  告警日志: .vibedev/monitoring/alerts/ (按日期分文件)
  质量报告: .vibedev/monitoring/quality/ (按feature_name分文件)

文件结构:
  .vibedev/monitoring/
  ├── realtime.md              # 当前实时状态
  ├── history/
  │   ├── 2024-01-15.md        # 每日历史数据
  │   ├── 2024-01-16.md
  │   └── ...
  ├── alerts/
  │   ├── 2024-01-15.md        # 每日告警日志
  │   ├── 2024-01-16.md
  │   └── ...
  └── quality/
      ├── user-authentication.md  # 按功能分组的质量数据
      ├── payment-integration.md
      └── ...

数据格式 (Markdown表格):
  | 时间戳 | 指标名称 | 数值 | 功能名称 | 代理名称 | 状态 |
  |--------|----------|------|----------|----------|------|
  | 2024-01-15T10:30:00Z | code_quality_score | 85 | user-auth | spec-developer | normal |
```

#### 监控文件模板

##### realtime.md 模板

```markdown
# 实时监控状态

## 更新时间
2024-01-15T10:30:00Z

## 当前代理状态
| 代理名称 | 状态 | 当前任务 | 开始时间 | 预计完成时间 |
|----------|------|----------|----------|--------------|
| spec-developer | 运行中 | 实现用户登录 | 10:15:00 | 10:45:00 |
| spec-code-reviewer | 等待中 | - | - | - |

## 实时质量指标
| 功能名称 | 代码质量分数 | 测试覆盖率 | 安全漏洞 | 技术债务 |
|----------|--------------|------------|----------|----------|
| user-authentication | 85 | 82% | 0 | 低 |
| payment-integration | 78 | 75% | 1(中危) | 中等 |

## 当前告警
| 级别 | 时间 | 功能 | 描述 | 状态 |
|------|------|------|------|------|
| 警告 | 10:25:00 | payment | 中危安全漏洞 | 处理中 |

## 性能指标
- CPU使用率: 45%
- 内存使用率: 62%
- 平均响应时间: 180ms
```

##### history/YYYY-MM-DD.md 模板

```markdown
# 历史监控数据 - 2024-01-15

## 代码质量趋势
| 时间 | 功能名称 | 质量分数 | 覆盖率 | 复杂度 | 重复率 |
|------|----------|----------|--------|--------|--------|
| 09:00 | user-auth | 82 | 80% | 6.5 | 3% |
| 10:00 | user-auth | 85 | 82% | 6.2 | 2.8% |
| 11:00 | user-auth | 87 | 85% | 6.0 | 2.5% |

## 性能数据
| 时间 | API响应时间(ms) | 内存使用(MB) | CPU使用(%) | 错误率(%) |
|------|-----------------|--------------|------------|-----------|
| 09:00 | 195 | 480 | 42 | 0.1 |
| 10:00 | 180 | 520 | 45 | 0.0 |
| 11:00 | 175 | 510 | 43 | 0.0 |

## 代理工作记录
| 时间 | 代理 | 任务 | 状态 | 耗时(分钟) |
|------|------|------|------|------------|
| 09:15-09:45 | spec-developer | 实现登录API | 完成 | 30 |
| 09:45-10:00 | spec-code-reviewer | 代码审查 | 完成 | 15 |
| 10:00-10:30 | spec-tester | 编写测试 | 完成 | 30 |
```

##### alerts/YYYY-MM-DD.md 模板

```markdown
# 告警日志 - 2024-01-15

## 告警统计
- 严重告警: 0
- 警告告警: 2
- 信息告警: 5
- 已解决: 6
- 处理中: 1

## 告警详情
| 时间 | 级别 | 功能 | 指标 | 阈值 | 实际值 | 状态 | 处理时间 |
|------|------|------|------|------|--------|------|----------|
| 08:30 | 警告 | payment | 代码质量 | >75 | 72 | 已解决 | 15分钟 |
| 10:25 | 警告 | payment | 安全漏洞 | =0 | 1(中危) | 处理中 | - |
| 11:15 | 信息 | user-auth | 复杂度 | <8 | 8.2 | 已解决 | 5分钟 |

## 告警处理记录
### 告警ID: ALT-20240115-001
- **时间**: 08:30:00
- **级别**: 警告
- **功能**: payment-integration
- **问题**: 代码质量分数低于阈值
- **触发值**: 72分 (阈值: >75分)
- **处理过程**:
  1. 08:35 - spec-code-reviewer开始分析
  2. 08:40 - 发现代码重复率过高
  3. 08:45 - spec-refactor开始重构
  4. 08:55 - 重构完成，质量分数提升到78分
- **解决时间**: 25分钟
- **根本原因**: 代码重复率8.5%，超过5%阈值
- **预防措施**: 增强代码审查检查重复率
```

##### quality/feature_name.md 模板

```markdown
# 质量监控报告 - user-authentication

## 功能概览
- **功能名称**: user-authentication
- **开发状态**: 开发中
- **当前阶段**: 开发阶段
- **负责代理**: spec-developer
- **开始时间**: 2024-01-15T08:00:00Z

## 质量趋势
### 代码质量评分历史
| 日期 | 分数 | 变化 | 主要问题 |
|------|------|------|----------|
| 01-13 | 75 | +5 | 测试覆盖率不足 |
| 01-14 | 82 | +7 | 代码重复率改善 |
| 01-15 | 85 | +3 | 性能优化 |

### 详细指标
| 指标 | 当前值 | 目标值 | 状态 | 趋势 |
|------|--------|--------|------|------|
| 代码质量分数 | 85 | >85 | ✅ 达标 | ↗️ 上升 |
| 测试覆盖率 | 82% | >80% | ✅ 达标 | ↗️ 上升 |
| 圈复杂度 | 6.0 | <8 | ✅ 达标 | ↘️ 下降 |
| 代码重复率 | 2.5% | <5% | ✅ 达标 | ↘️ 下降 |
| 安全漏洞 | 0 | 0 | ✅ 达标 | ➡️ 稳定 |
| 技术债务 | 8分 | <10 | ✅ 达标 | ↘️ 下降 |

## 质量门控记录
| 门控 | 时间 | 结果 | 分数 | 问题 | 处理 |
|------|------|------|------|------|------|
| 门控1 | 01-13 09:00 | ❌ 失败 | 92% | 架构文档不完整 | 已补充 |
| 门控1 | 01-13 11:00 | ✅ 通过 | 96% | - | - |
| 门控2 | 01-15 10:00 | 🔄 进行中 | - | - | - |

## 代理工作记录
| 代理 | 任务数 | 完成数 | 成功率 | 平均耗时 | 质量评价 |
|------|--------|--------|--------|----------|----------|
| spec-analyst | 3 | 3 | 100% | 25分钟 | 优秀 |
| spec-architect | 2 | 2 | 100% | 45分钟 | 良好 |
| spec-developer | 8 | 6 | 75% | 35分钟 | 良好 |
| spec-code-reviewer | 6 | 6 | 100% | 15分钟 | 优秀 |

## 问题和改进
### 当前问题
- 无

### 历史问题
1. **代码重复率过高** (已解决)
   - 发现时间: 01-14 10:00
   - 解决时间: 01-14 14:00
   - 解决方案: spec-refactor重构公共代码

### 改进建议
1. 增加单元测试覆盖边缘情况
2. 优化数据库查询性能
3. 完善错误处理机制

### 告警规则定义

#### 代码质量告警

```yaml
严重告警 (立即通知):
  - 代码质量评分 < 60分
  - 高危安全漏洞 > 0
  - 测试覆盖率下降 > 20%
  - 技术债务分数 > 50

警告告警 (5分钟内通知):
  - 代码质量评分 60-75分
  - 中危安全漏洞 > 2
  - 代码重复率 > 8%
  - 圈复杂度 > 10

信息告警 (15分钟内通知):
  - 代码质量评分 75-85分
  - 低危安全漏洞 > 5
  - 技术债务分数 25-50
```

#### 性能告警

```yaml
严重告警:
  - API响应时间 > 1秒 (P95)
  - 内存使用率 > 90%
  - CPU使用率 > 95%
  - 错误率 > 5%

警告告警:
  - API响应时间 > 500ms (P95)
  - 内存使用率 > 80%
  - CPU使用率 > 80%
  - 错误率 > 2%

信息告警:
  - API响应时间 > 300ms (P95)
  - 内存使用率 > 70%
  - CPU使用率 > 70%
  - 错误率 > 1%
```

#### 工作流告警

```yaml
严重告警:
  - 质量门控失败 > 3次
  - 代理执行超时 > 30分钟
  - 用户交互无响应 > 10分钟

警告告警:
  - 质量门控失败 2-3次
  - 代理执行时间 > 15分钟
  - 任务完成率 < 80%

信息告警:
  - 质量门控失败 1次
  - 代理执行时间 > 10分钟
  - 任务完成率 80-90%
```

### 告警通知机制

#### 通知渠道

```yaml
通知优先级:
  严重告警:
    - 立即通知: 控制台弹窗
    - 1分钟内: 系统通知
    - 5分钟内: 邮件通知

  警告告警:
    - 5分钟内: 控制台显示
    - 15分钟内: 系统通知

  信息告警:
    - 15分钟内: 控制台显示
    - 1小时内: 汇总邮件
```

#### 告警抑制规则

```yaml
抑制策略:
  - 相同告警5分钟内只发送一次
  - 严重告警升级: 连续3次警告告警升级为严重告警
  - 恢复通知: 告警解除时发送恢复通知
  - 静默时间: 非工作时间降低告警频率
```

### 监控仪表板

#### 实时仪表板

```yaml
主要面板:
  - 代码质量趋势图
  - 性能指标实时图表
  - 代理工作状态
  - 质量门控通过率
  - 当前告警列表

刷新频率:
  - 实时数据: 5秒
  - 历史趋势: 30秒
  - 告警状态: 实时
```

#### 历史分析面板

```yaml
分析维度:
  - 按时间段分析
  - 按功能模块分析
  - 按代理类型分析
  - 按质量指标分析

报表功能:
  - 日报: 每日质量和性能摘要
  - 周报: 趋势分析和改进建议
  - 月报: 整体质量评估和规划建议
```

## 版本控制和回滚策略

### Git工作流规范

#### 分支策略

```yaml
分支命名规范:
  - 主分支: main
  - 开发分支: develop
  - 功能分支: feature/{feature_name}
  - 修复分支: hotfix/{issue_id}
  - 发布分支: release/{version}

分支保护规则:
  - main分支: 禁止直接推送，需要PR审查
  - develop分支: 需要通过CI检查
  - feature分支: 需要通过质量门控
```

#### 自动提交策略

```yaml
提交频率:
  - 每完成一个任务: 自动提交
  - 每通过质量门控: 创建标签
  - 每日结束: 创建快照

提交信息格式:
  type(scope): description

  类型:
    - feat: 新功能
    - fix: 修复
    - refactor: 重构
    - test: 测试
    - docs: 文档
    - style: 格式

  示例:
    feat(user-auth): 添加用户登录功能
    fix(payment): 修复支付金额计算错误
    refactor(api): 优化用户查询接口性能
```

### 回滚机制

#### 自动回滚触发条件

```yaml
严重问题自动回滚:
  - 质量门控连续失败3次
  - 代码质量评分 < 50分
  - 高危安全漏洞 > 0
  - 系统错误率 > 10%
  - 性能下降 > 50%

警告级别人工确认回滚:
  - 质量门控失败2次
  - 代码质量评分 50-60分
  - 中危安全漏洞 > 5
  - 性能下降 20-50%
```

#### 回滚执行流程

```yaml
回滚步骤:
  1. 问题检测: 监控系统发现问题
  2. 影响评估: 评估问题影响范围
  3. 回滚决策: 自动或人工决策回滚
  4. 执行回滚: 回滚到最近稳定版本
  5. 验证回滚: 确认问题已解决
  6. 通知相关: 通知用户和团队
  7. 问题分析: 分析问题原因
  8. 修复计划: 制定修复方案

回滚类型:
  - 代码回滚: 回滚到上一个稳定提交
  - 配置回滚: 回滚配置文件
  - 数据回滚: 回滚数据库变更 (谨慎)
  - 依赖回滚: 回滚依赖版本
```

#### 回滚验证

```yaml
验证检查项:
  - 代码编译成功
  - 所有测试通过
  - 质量门控通过
  - 性能指标恢复
  - 安全扫描通过
  - 功能验证通过

验证超时:
  - 编译检查: 5分钟
  - 测试执行: 15分钟
  - 质量检查: 10分钟
  - 性能验证: 10分钟
  - 总超时: 45分钟
```

### 版本标签管理

#### 标签命名规范

```yaml
标签格式: v{major}.{minor}.{patch}-{stage}

版本号规则:
  - major: 重大功能变更
  - minor: 新功能添加
  - patch: 错误修复
  - stage: alpha/beta/rc/stable

示例:
  - v1.0.0-alpha: 第一个alpha版本
  - v1.0.0-beta.1: 第一个beta版本
  - v1.0.0-rc.1: 第一个候选版本
  - v1.0.0: 正式版本
```

#### 自动标签创建

```yaml
标签创建时机:
  - 质量门控1通过: v{version}-planning
  - 质量门控2通过: v{version}-development
  - 质量门控3通过: v{version}-stable
  - 发布到生产: v{version}-production

标签信息包含:
  - 版本号
  - 创建时间
  - 质量评分
  - 功能列表
  - 已知问题
  - 回滚信息
```

## 异常恢复流程标准化

### 异常分类和处理

#### 异常类型定义

```yaml
代码异常:
  - 语法错误: 编译失败
  - 逻辑错误: 测试失败
  - 性能问题: 响应超时
  - 安全漏洞: 安全扫描失败

系统异常:
  - 内存不足: OOM错误
  - 磁盘空间不足: 存储错误
  - 网络异常: 连接超时
  - 服务异常: 依赖服务不可用

工作流异常:
  - 代理超时: 执行时间过长
  - 代理失败: 代理执行错误
  - 质量门控失败: 质量标准未达到
  - 用户交互超时: 用户无响应
```

### 标准化恢复程序

#### 代码异常恢复

```yaml
语法错误恢复:
  1. 错误检测: 编译器报告语法错误
  2. 错误定位: 定位具体错误位置
  3. 自动修复: 尝试自动修复常见语法错误
  4. 人工干预: 复杂错误请求人工修复
  5. 验证修复: 重新编译验证
  6. 继续执行: 修复成功后继续工作流

逻辑错误恢复:
  1. 测试失败: 单元测试或集成测试失败
  2. 错误分析: 分析失败原因
  3. 代码审查: spec-code-reviewer分析代码
  4. 修复建议: 提供具体修复建议
  5. 实施修复: spec-developer实施修复
  6. 重新测试: spec-tester重新执行测试
  7. 验证通过: 确认问题解决

性能问题恢复:
  1. 性能检测: 发现性能瓶颈
  2. 性能分析: spec-performance-optimizer分析
  3. 优化建议: 提供性能优化方案
  4. 实施优化: 应用优化方案
  5. 性能验证: 验证优化效果
  6. 基准更新: 更新性能基准
```

#### 系统异常恢复

```yaml
资源不足恢复:
  1. 资源监控: 检测资源使用情况
  2. 资源清理: 清理临时文件和缓存
  3. 进程优化: 优化内存和CPU使用
  4. 资源扩展: 必要时请求更多资源
  5. 恢复执行: 资源充足后恢复执行

网络异常恢复:
  1. 网络检测: 检测网络连接状态
  2. 重试机制: 指数退避重试
  3. 降级服务: 使用本地缓存或备用服务
  4. 网络恢复: 等待网络恢复
  5. 服务恢复: 恢复正常服务
```

#### 工作流异常恢复

```yaml
代理超时恢复:
  1. 超时检测: 监控代理执行时间
  2. 进度检查: 检查代理执行进度
  3. 任务分解: 将大任务分解为小任务
  4. 重新执行: 重新启动代理执行
  5. 并行执行: 可能的情况下并行执行
  6. 人工干预: 必要时请求人工干预

质量门控失败恢复:
  1. 失败分析: 分析具体失败原因
  2. 问题定位: 定位质量问题所在
  3. 修复计划: 制定具体修复计划
  4. 执行修复: 按计划执行修复
  5. 重新验证: 重新执行质量门控
  6. 循环改进: 持续改进直到通过
```

### 恢复时间目标

#### RTO (恢复时间目标)

```yaml
异常类型恢复时间:
  - 语法错误: < 5分钟
  - 逻辑错误: < 30分钟
  - 性能问题: < 60分钟
  - 安全漏洞: < 15分钟
  - 系统异常: < 10分钟
  - 代理超时: < 5分钟
  - 质量门控失败: < 45分钟

总体目标:
  - 95%的异常在30分钟内恢复
  - 99%的异常在2小时内恢复
  - 100%的异常在4小时内恢复
```

#### RPO (恢复点目标)

```yaml
数据丢失容忍度:
  - 代码变更: 0 (实时备份)
  - 配置文件: < 5分钟
  - 测试结果: < 15分钟
  - 监控数据: < 30分钟
  - 日志数据: < 60分钟
```

### 恢复验证和报告

#### 恢复验证清单

```yaml
验证项目:
  - [ ] 系统功能正常
  - [ ] 性能指标达标
  - [ ] 安全扫描通过
  - [ ] 数据完整性确认
  - [ ] 用户访问正常
  - [ ] 监控告警清除
  - [ ] 备份系统正常

验证超时: 30分钟
验证失败处理: 重新执行恢复流程
```

#### 异常报告生成

```yaml
报告内容:
  - 异常发生时间
  - 异常类型和级别
  - 影响范围和程度
  - 恢复执行步骤
  - 恢复耗时
  - 根本原因分析
  - 预防措施建议
  - 流程改进建议

报告生成: 异常恢复完成后自动生成
报告分发: 自动发送给相关人员
报告存档: 保存到知识库供后续参考
```

## 代理工作流使用指南

### 启动代理工作流

#### 使用spec-orchestrator启动完整工作流

```bash
使用 spec-orchestrator 代理并说：创建一个带用户认证的待办事项Web应用
```

#### 从特定阶段开始

```bash
# 仅执行规划阶段
使用 spec-orchestrator 代理：--phase planning "创建任务跟踪器"

# 从开发阶段开始
使用 spec-orchestrator 代理：--phase development --from-artifacts ./.vibedev/specs/{feature_name}/

# 验证现有代码
使用 spec-orchestrator 代理：--phase validation --project-path ./my-project/
```

#### 直接使用单个代理

```bash
# 需求分析
使用 spec-analyst 代理：分析社交媒体仪表板的需求

# 架构设计
使用 spec-architect 代理：从 ./.vibedev/specs/{feature_name}/requirements.md 设计架构

# 任务规划
使用 spec-planner 代理：从 ./.vibedev/specs/{feature_name}/architecture.md 创建任务分解

# 代码实现
使用 spec-developer 代理：从 ./.vibedev/specs/{feature_name}/tasks.md 实现 TASK-001

# 测试
使用 spec-tester 代理：为 ./src/auth/ 编写测试

# 代码审查
使用 spec-reviewer 代理：审查 ./src/ 中的代码

# 最终验证
使用 spec-validator 代理：验证 ./my-app/ 中的项目
```

### 质量门控处理

当质量门控失败时：

1. **自动反馈**: 系统会提供具体的改进建议
2. **迭代改进**: 相关代理会自动进行修正
3. **人工干预**: 可选择手动调整质量阈值或跳过特定检查

### 与传统工作流的兼容性

- 新的代理工作流完全兼容现有的`.vibedev/specs/{feature_name}/`目录结构
- 可以在代理工作流和传统批准工作流之间切换
- 支持渐进式采用：从简单功能开始使用代理工作流

## 核心原则总结

### 🚨 关键要求 - 进入开发阶段前的强制确认

**无论使用哪种工作流模式（代理工作流或传统批准工作流），都必须遵循以下核心原则：**

1. **不确认清楚各种事项，不要进入开发阶段**
2. **如果还有什么问题要问用户，则提问**
3. **基本没有什么问题的情况下再询问是否进入开发阶段**

### 强制确认适用于所有执行模式

- **标准模式**: 每个主要阶段完成后请求用户确认，进入开发阶段前强制确认所有事项清楚
- **vibe模式**: 即使在快速执行模式下，进入开发阶段前仍需确认所有关键事项清楚
- **代理自动化**: 即使在高度自动化的代理协调下，关键决策点仍需用户参与确认

### 禁止行为

- ❌ 对不确定的事项进行假设
- ❌ 在有疑问时直接进入开发阶段
- ❌ 跳过必要的用户确认步骤

### 必须行为

- ✅ 主动提问澄清不清楚的地方
- ✅ 逐项确认关键决策点
- ✅ 确保所有需求、架构、任务都已明确
- ✅ 获得用户明确同意后才进入开发阶段
- ✅ **AI自动编程特殊要求**:
  - 实时监控代码质量和性能指标
  - 自动化工具链集成和错误恢复
  - 增量开发和持续质量检查
  - 透明的开发过程和决策记录

---

_使用专业化代理系统在整个开发过程中保持对这些规则的遵循。_


# Rust重写项目特定规则

### 项目结构

- **Rust重写项目根目录**: `./coco-server-rust`
- **原Go项目根目录**: `./`
- **使用工具**: legacy-modernizer进行渐进式重写
- **代码规范**: 使用rust-pro写rust代码

### 端到端测试方法

- **服务器端**: 在`./coco-server-rust`运行`cargo run`
- **客户端**: 在`/Users/<USER>/local_doc/l_dev/my/rust/aigui/coco-app`运行`pnpm tauri dev`
- **连接设置**: 需要用户手工操作添加服务器配置

### 文档资源

- **服务器deepwiki文档**: <https://deepwiki.com/infinilabs/coco-server>
- **客户端deepwiki文档**: <https://deepwiki.com/infinilabs/coco-app>
- **服务器文档**: <https://zread.ai/infinilabs/coco-server>
- **客户端文档**: <https://zread.ai/infinilabs/coco-app>

### 重写要求

- **API兼容性**: Rust重写的服务端必须与原Go服务端在API路径和行为上完全一致
- **客户端兼容**: 确保coco app能够正常连接和使用重写后的服务端
- **严格规范**: 要仔细研究原来的客户端具体请求的端点，不能假设
- **数据库替换**: 原有的Elasticsearch用SurrealDB代替
