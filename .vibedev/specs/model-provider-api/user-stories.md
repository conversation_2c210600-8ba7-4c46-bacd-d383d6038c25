# 模型提供商API用户故事

## 史诗: 模型提供商管理

### 故事: MP-001 - 创建模型提供商
**作为** 系统管理员
**我希望** 创建新的模型提供商配置
**以便** 集成不同的AI服务提供商

**验收标准** (EARS格式):
- **当** 发送POST请求到/model_provider/且包含有效数据 **那么** 返回201状态码和创建的ID
- **当** 发送POST请求但缺少必填字段 **那么** 返回400状态码和验证错误信息
- **当** 发送POST请求但名称已存在 **那么** 返回409状态码和冲突错误信息
- **如果** 请求数据有效 **那么** 自动设置builtin=false和时间戳
- **对于** 创建的提供商 **验证** ID格式为字符串且唯一

**技术说明**:
- 使用SurrealDB的create操作
- 实现名称唯一性检查
- 自动生成UUID作为ID

**故事点数**: 5
**优先级**: 高

### 故事: MP-002 - 获取模型提供商详情
**作为** 开发者
**我希望** 根据ID获取模型提供商的详细信息
**以便** 在应用中使用特定的提供商配置

**验收标准** (EARS格式):
- **当** 发送GET请求到/model_provider/{valid_id} **那么** 返回200状态码和完整数据
- **当** 发送GET请求到/model_provider/{invalid_id} **那么** 返回404状态码
- **如果** 用户无权限 **那么** 返回403状态码
- **对于** 返回的数据 **验证** 不包含api_key等敏感字段
- **对于** 响应格式 **验证** 符合{"_id": "xxx", "found": true, "_source": {...}}

**技术说明**:
- 实现敏感字段过滤
- 使用标准的Elasticsearch兼容响应格式

**故事点数**: 3
**优先级**: 高

### 故事: MP-003 - 更新模型提供商
**作为** 系统管理员
**我希望** 更新现有模型提供商的配置
**以便** 修改API密钥或其他设置

**验收标准** (EARS格式):
- **当** 发送PUT请求到/model_provider/{id}且数据有效 **那么** 返回200状态码
- **当** 尝试修改内置提供商的名称 **那么** 保持原名称不变
- **当** 尝试修改builtin或created字段 **那么** 保持原值不变
- **如果** 新名称与其他提供商冲突 **那么** 返回409状态码
- **对于** 更新操作 **验证** updated时间戳被自动更新

**技术说明**:
- 实现字段保护逻辑
- 检查名称唯一性（排除自身）

**故事点数**: 5
**优先级**: 高

### 故事: MP-004 - 删除模型提供商
**作为** 系统管理员
**我希望** 删除不需要的模型提供商
**以便** 清理系统配置

**验收标准** (EARS格式):
- **当** 发送DELETE请求到/model_provider/{id}且为非内置提供商 **那么** 返回200状态码
- **当** 尝试删除内置提供商 **那么** 返回403状态码和保护错误信息
- **当** 删除不存在的提供商 **那么** 返回404状态码
- **对于** 删除响应 **验证** 格式为{"_id": "xxx", "result": "deleted"}

**技术说明**:
- 实现内置提供商保护检查
- 使用软删除或硬删除策略

**故事点数**: 3
**优先级**: 高

### 故事: MP-005 - 搜索模型提供商（基础版本）
**作为** 用户
**我希望** 搜索和过滤模型提供商列表
**以便** 快速找到需要的提供商

**验收标准** (EARS格式):
- **当** 发送GET请求到/model_provider/_search **那么** 返回分页的提供商列表
- **当** 发送POST请求到/model_provider/_search带查询体 **那么** 返回过滤后的结果
- **如果** 包含size参数 **那么** 限制返回结果数量
- **如果** 包含from参数 **那么** 从指定位置开始返回
- **如果** 包含sort参数 **那么** 按指定字段排序
- **如果** 包含基础filter参数 **那么** 按精确匹配过滤结果
- **如果** 包含简单文本搜索 **那么** 支持name和description的包含匹配
- **对于** 搜索结果 **验证** 不包含敏感字段
- **注意**: 复杂的Elasticsearch查询语法暂不支持

**技术说明**:
- 实现基础查询构建器
- 支持简单文本包含匹配（LIKE操作）
- 支持精确匹配过滤
- 实现分页和排序
- 暂不支持复杂聚合和全文搜索

**故事点数**: 5
**优先级**: 高

### 故事: MP-006 - 启用/禁用提供商
**作为** 系统管理员
**我希望** 快速启用或禁用模型提供商
**以便** 控制哪些提供商可用

**验收标准** (EARS格式):
- **当** 更新enabled字段为true **那么** 提供商变为可用状态
- **当** 更新enabled字段为false **那么** 提供商变为不可用状态
- **如果** 搜索时filter enabled=true **那么** 只返回启用的提供商
- **对于** 状态变更 **验证** updated时间戳被更新

**技术说明**:
- 使用布尔字段控制状态
- 在搜索中支持状态过滤

**故事点数**: 2
**优先级**: 中

### 故事: MP-007 - 模型配置管理
**作为** 系统管理员
**我希望** 为每个提供商配置支持的模型列表
**以便** 定义可用的AI模型

**验收标准** (EARS格式):
- **当** 创建提供商时包含models数组 **那么** 保存模型配置
- **当** 更新提供商的models字段 **那么** 更新模型列表
- **如果** 模型包含settings **那么** 验证设置格式正确
- **对于** 模型设置 **验证** 包含temperature, top_p等参数

**技术说明**:
- 支持嵌套的模型配置对象
- 验证模型设置参数范围

**故事点数**: 5
**优先级**: 中

### 故事: MP-008 - 权限控制（暂缓实现）
**作为** 系统
**我希望** 控制用户对模型提供商的访问权限
**以便** 确保安全性

**验收标准** (EARS格式):
- **当前阶段**: 暂不实现权限检查，所有API开放访问
- **如果** 在生产环境部署 **那么** 需要先实现权限控制
- **对于** 开发和测试环境 **验证** 可以正常访问所有端点

**技术说明**:
- 当前版本跳过权限检查
- 后续版本集成完整权限系统
- 预留权限检查的代码结构

**故事点数**: 0（暂缓）
**优先级**: 低（后期实现）

### 故事: MP-009 - 错误处理
**作为** 开发者
**我希望** 收到清晰的错误信息
**以便** 快速定位和解决问题

**验收标准** (EARS格式):
- **当** 发生验证错误 **那么** 返回400状态码和详细错误信息
- **当** 资源不存在 **那么** 返回404状态码和标准错误格式
- **当** 发生权限错误 **那么** 返回403状态码和权限错误信息
- **当** 发生服务器错误 **那么** 返回500状态码和通用错误信息
- **对于** 所有错误 **验证** 使用一致的错误响应格式

**技术说明**:
- 实现统一的错误处理中间件
- 提供有意义的错误消息

**故事点数**: 3
**优先级**: 中
