# 技术栈选择和配置

## 技术栈概览

本文档详细说明了模型提供商API项目的技术栈选择、配置和最佳实践。

## 核心技术栈

### 1. Web框架 - Axum

**选择理由**：
- 高性能异步Web框架，基于Tokio
- 类型安全的路由系统
- 优秀的中间件支持
- 与现有项目架构一致

**版本**: `axum = "0.7"`

**核心特性**：
```rust
// 路由定义
use axum::{
    routing::{get, post, put, delete, options},
    Router, Json, extract::{Path, Query, State},
    response::<PERSON><PERSON> as ResponseJson,
    middleware,
};

// 中间件支持
use tower_http::{
    cors::CorsLayer,
    trace::TraceLayer,
    timeout::TimeoutLayer,
};
```

**配置示例**：
```rust
let app = Router::new()
    .route("/model_provider/", post(create_handler))
    .route("/model_provider/:id", get(get_handler))
    .layer(CorsLayer::very_permissive())
    .layer(TraceLayer::new_for_http())
    .layer(TimeoutLayer::new(Duration::from_secs(30)))
    .with_state(app_state);
```

### 2. 数据库 - SurrealDB

**选择理由**：
- 现代多模型数据库
- 原生支持JSON文档
- 强大的查询能力
- 项目已选择的技术

**版本**: `surrealdb = "1.0"`

**连接配置**：
```rust
use surrealdb::{Surreal, engine::remote::ws::Ws};

// 数据库连接
let db = Surreal::new::<Ws>("127.0.0.1:8000").await?;
db.use_ns("coco").use_db("model_providers").await?;
```

**查询示例**：
```rust
// 创建记录
let created: Vec<ModelProvider> = db
    .create("model_provider")
    .content(provider)
    .await?;

// 查询记录
let provider: Option<ModelProvider> = db
    .select(("model_provider", id))
    .await?;

// 搜索查询
let results: Vec<ModelProvider> = db
    .query("SELECT * FROM model_provider WHERE name CONTAINS $name LIMIT $limit START $start")
    .bind(("name", search_term))
    .bind(("limit", size))
    .bind(("start", from))
    .await?;
```

### 3. 序列化 - Serde

**选择理由**：
- Rust生态系统标准序列化库
- 强大的derive宏支持
- 灵活的字段控制
- JSON处理优秀

**版本**: `serde = { version = "1.0", features = ["derive"] }`

**使用示例**：
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelProvider {
    pub id: String,
    #[serde(with = "chrono::serde::ts_seconds")]
    pub created: DateTime<Utc>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub api_key: Option<String>,
    #[serde(default)]
    pub enabled: bool,
}
```

### 4. 异步运行时 - Tokio

**选择理由**：
- Rust异步编程标准运行时
- 优秀的性能和稳定性
- 丰富的异步生态系统
- Axum的底层依赖

**版本**: `tokio = { version = "1.0", features = ["full"] }`

**配置**：
```rust
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 应用启动逻辑
}
```

## 支持库和工具

### 1. 错误处理

**thiserror** - 错误类型定义
```rust
// Cargo.toml
thiserror = "1.0"

// 使用示例
#[derive(Debug, thiserror::Error)]
pub enum ApiError {
    #[error("Validation failed: {0}")]
    Validation(String),
    #[error("Not found: {0}")]
    NotFound(String),
}
```

**anyhow** - 错误传播
```rust
// Cargo.toml
anyhow = "1.0"

// 使用示例
use anyhow::{Result, Context};

async fn process() -> Result<()> {
    some_operation()
        .await
        .context("Failed to process request")?;
    Ok(())
}
```

### 2. 日志和追踪

**tracing** - 结构化日志
```rust
// Cargo.toml
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

// 配置
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

tracing_subscriber::registry()
    .with(tracing_subscriber::EnvFilter::new(
        std::env::var("RUST_LOG").unwrap_or_else(|_| "info".into()),
    ))
    .with(tracing_subscriber::fmt::layer())
    .init();

// 使用
#[tracing::instrument]
async fn create_provider(req: CreateRequest) -> Result<String> {
    tracing::info!("Creating new model provider: {}", req.name);
    // 实现逻辑
}
```

### 3. 时间处理

**chrono** - 日期时间处理
```rust
// Cargo.toml
chrono = { version = "0.4", features = ["serde"] }

// 使用示例
use chrono::{DateTime, Utc};

#[derive(Serialize, Deserialize)]
pub struct ModelProvider {
    #[serde(with = "chrono::serde::ts_seconds")]
    pub created: DateTime<Utc>,
    #[serde(with = "chrono::serde::ts_seconds")]
    pub updated: DateTime<Utc>,
}
```

### 4. UUID生成

**uuid** - 唯一标识符
```rust
// Cargo.toml
uuid = { version = "1.0", features = ["v4", "serde"] }

// 使用示例
use uuid::Uuid;

fn generate_id() -> String {
    Uuid::new_v4().to_string()
}
```

### 5. 配置管理

**config** - 配置文件处理
```rust
// Cargo.toml
config = "0.13"

// 配置结构
#[derive(Debug, Deserialize)]
pub struct AppConfig {
    pub database: DatabaseConfig,
    pub server: ServerConfig,
    pub cache: CacheConfig,
}

#[derive(Debug, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
    pub namespace: String,
    pub database: String,
}
```

### 6. 验证

**validator** - 数据验证
```rust
// Cargo.toml
validator = { version = "0.16", features = ["derive"] }

// 使用示例
use validator::{Validate, ValidationError};

#[derive(Debug, Deserialize, Validate)]
pub struct CreateModelProviderRequest {
    #[validate(length(min = 1, max = 100))]
    pub name: String,
    
    #[validate(url)]
    pub base_url: String,
    
    #[validate(length(min = 1))]
    pub api_key: String,
}
```

## 开发工具

### 1. 代码格式化和检查

**rustfmt** - 代码格式化
```toml
# rustfmt.toml
max_width = 100
hard_tabs = false
tab_spaces = 4
```

**clippy** - 代码检查
```bash
cargo clippy -- -D warnings
```

### 2. 测试框架

**tokio-test** - 异步测试
```rust
// Cargo.toml
[dev-dependencies]
tokio-test = "0.4"

// 测试示例
#[tokio::test]
async fn test_create_provider() {
    let service = create_test_service().await;
    let request = CreateModelProviderRequest {
        name: "Test Provider".to_string(),
        api_type: "openai".to_string(),
        base_url: "https://api.test.com".to_string(),
        api_key: "test-key".to_string(),
        enabled: true,
        description: "Test description".to_string(),
        icon: "test-icon".to_string(),
        models: vec![],
    };
    
    let result = service.create(request).await;
    assert!(result.is_ok());
}
```

**mockall** - Mock测试
```rust
// Cargo.toml
[dev-dependencies]
mockall = "0.11"

// Mock示例
use mockall::{automock, predicate::*};

#[automock]
#[async_trait]
pub trait ModelProviderRepository {
    async fn create(&self, provider: &ModelProvider) -> Result<String>;
}
```

### 3. 性能测试

**criterion** - 基准测试
```rust
// Cargo.toml
[dev-dependencies]
criterion = { version = "0.5", features = ["html_reports"] }

// 基准测试
use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn benchmark_search(c: &mut Criterion) {
    c.bench_function("search_providers", |b| {
        b.iter(|| {
            // 测试搜索性能
        })
    });
}

criterion_group!(benches, benchmark_search);
criterion_main!(benches);
```

## 部署配置

### 1. Docker配置

**Dockerfile**:
```dockerfile
FROM rust:1.75 as builder

WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/coco-server /usr/local/bin/coco-server

EXPOSE 9000
CMD ["coco-server"]
```

### 2. 环境配置

**环境变量**:
```bash
# 数据库配置
SURREALDB_URL=ws://localhost:8000
SURREALDB_NAMESPACE=coco
SURREALDB_DATABASE=model_providers

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=9000

# 日志配置
RUST_LOG=info

# 缓存配置
CACHE_TTL_SECONDS=1800
```

### 3. 监控配置

**Prometheus指标**:
```rust
// Cargo.toml
prometheus = "0.13"
axum-prometheus = "0.4"

// 指标收集
use axum_prometheus::PrometheusMetricLayer;

let (prometheus_layer, metric_handle) = PrometheusMetricLayer::pair();

let app = Router::new()
    .route("/metrics", get(|| async move { metric_handle.render() }))
    .layer(prometheus_layer);
```

## 性能优化配置

### 1. 编译优化

**Cargo.toml**:
```toml
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
```

### 2. 运行时优化

**Tokio配置**:
```rust
#[tokio::main(flavor = "multi_thread", worker_threads = 4)]
async fn main() {
    // 应用逻辑
}
```

### 3. 内存优化

**缓存配置**:
```rust
// 限制缓存大小
const MAX_CACHE_SIZE: usize = 10000;
const CACHE_TTL: Duration = Duration::from_secs(1800); // 30分钟
```

## 安全配置

### 1. HTTPS配置

```rust
// TLS支持
use axum_server::tls_rustls::RustlsConfig;

let config = RustlsConfig::from_pem_file("cert.pem", "key.pem").await?;
axum_server::bind_rustls("0.0.0.0:443".parse()?, config)
    .serve(app.into_make_service())
    .await?;
```

### 2. 安全头部

```rust
use tower_http::set_header::SetResponseHeaderLayer;

let app = Router::new()
    .layer(SetResponseHeaderLayer::if_not_present(
        header::X_CONTENT_TYPE_OPTIONS,
        HeaderValue::from_static("nosniff"),
    ))
    .layer(SetResponseHeaderLayer::if_not_present(
        header::X_FRAME_OPTIONS,
        HeaderValue::from_static("DENY"),
    ));
```

## 依赖版本管理

### 完整的Cargo.toml

```toml
[package]
name = "coco-model-provider-api"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web框架
axum = { version = "0.7", features = ["macros"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace", "timeout"] }

# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# 数据库
surrealdb = "1.0"

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID
uuid = { version = "1.0", features = ["v4", "serde"] }

# 配置
config = "0.13"

# 验证
validator = { version = "0.16", features = ["derive"] }

[dev-dependencies]
tokio-test = "0.4"
mockall = "0.11"
criterion = { version = "0.5", features = ["html_reports"] }

[[bench]]
name = "search_benchmark"
harness = false
```
