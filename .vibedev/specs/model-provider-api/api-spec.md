# 模型提供商API规格说明

## API概述

本文档定义了模型提供商管理API的详细规格，确保与Go版本完全兼容。所有API端点都遵循RESTful设计原则，使用JSON格式进行数据交换。

## 基础信息

- **Base URL**: `http://localhost:9000` (开发环境)
- **Content-Type**: `application/json`
- **字符编码**: UTF-8
- **API版本**: v1

## 认证和授权

**当前阶段**: 暂不实现认证和权限控制
**后续版本**: 将集成JWT认证和基于角色的权限控制

## API端点详细规格

### 1. 创建模型提供商

**端点**: `POST /model_provider/`

**请求格式**:

```json
{
  "name": "string (必填, 1-100字符)",
  "api_key": "string (必填)",
  "api_type": "string (必填, 可选值: openai, ollama)",
  "base_url": "string (必填, 有效URL)",
  "icon": "string (可选)",
  "models": [
    {
      "name": "string (必填)",
      "settings": {
        "temperature": "number (可选, 0.0-2.0)",
        "top_p": "number (可选, 0.0-1.0)",
        "presence_penalty": "number (可选, -2.0-2.0)",
        "frequency_penalty": "number (可选, -2.0-2.0)",
        "max_tokens": "integer (可选, 1-32768)"
      }
    }
  ],
  "enabled": "boolean (可选, 默认true)",
  "description": "string (可选)"
}
```

**请求示例**:

```json
{
  "name": "DeepSeek AI",
  "api_key": "sk-xxxxxxxxxxxxxxxx",
  "api_type": "openai",
  "base_url": "https://api.deepseek.com/v1",
  "icon": "font_deepseek",
  "models": [
    {
      "name": "deepseek-chat",
      "settings": {
        "temperature": 0.8,
        "top_p": 0.9,
        "max_tokens": 4096
      }
    }
  ],
  "enabled": true,
  "description": "DeepSeek AI模型提供商"
}
```

**响应格式**:

```json
{
  "_id": "cvj0hjlath21mqh6jbh0",
  "result": "created"
}
```

**状态码**:

- `201 Created`: 创建成功
- `400 Bad Request`: 请求参数无效
- `409 Conflict`: 名称已存在
- `500 Internal Server Error`: 服务器内部错误

### 2. 获取模型提供商

**端点**: `GET /model_provider/{id}`

**路径参数**:

- `id`: 模型提供商ID (必填)

**响应格式**:

```json
{
  "_id": "cvj0hjlath21mqh6jbh0",
  "found": true,
  "_source": {
    "id": "cvj0hjlath21mqh6jbh0",
    "created": "2024-01-15T10:30:00Z",
    "updated": "2024-01-15T10:30:00Z",
    "name": "DeepSeek AI",
    "api_type": "openai",
    "base_url": "https://api.deepseek.com/v1",
    "icon": "font_deepseek",
    "models": [
      {
        "name": "deepseek-chat",
        "settings": {
          "temperature": 0.8,
          "top_p": 0.9,
          "max_tokens": 4096
        }
      }
    ],
    "enabled": true,
    "builtin": false,
    "description": "DeepSeek AI模型提供商"
  }
}
```

**注意**: 响应中不包含`api_key`等敏感字段

**状态码**:

- `200 OK`: 获取成功
- `404 Not Found`: 提供商不存在
- `500 Internal Server Error`: 服务器内部错误

### 3. 更新模型提供商

**端点**: `PUT /model_provider/{id}`

**路径参数**:

- `id`: 模型提供商ID (必填)

**请求格式**: 与创建请求相同，所有字段都是可选的

**请求示例**:

```json
{
  "api_key": "sk-new-key-xxxxxxxx",
  "enabled": false,
  "description": "更新后的描述"
}
```

**响应格式**:

```json
{
  "_id": "cvj0hjlath21mqh6jbh0",
  "result": "updated"
}
```

**业务规则**:

- 内置提供商的`name`字段不能修改
- `builtin`和`created`字段不能修改
- 如果修改`name`，需要检查唯一性
- 自动更新`updated`时间戳

**状态码**:

- `200 OK`: 更新成功
- `400 Bad Request`: 请求参数无效
- `404 Not Found`: 提供商不存在
- `409 Conflict`: 名称冲突
- `500 Internal Server Error`: 服务器内部错误

### 4. 删除模型提供商

**端点**: `DELETE /model_provider/{id}`

**路径参数**:

- `id`: 模型提供商ID (必填)

**响应格式**:

```json
{
  "_id": "cvj0hjlath21mqh6jbh0",
  "result": "deleted"
}
```

**业务规则**:

- 内置提供商不能删除
- 删除操作不可逆

**状态码**:

- `200 OK`: 删除成功
- `403 Forbidden`: 尝试删除内置提供商
- `404 Not Found`: 提供商不存在
- `500 Internal Server Error`: 服务器内部错误

### 5. 搜索模型提供商

#### 5.1 GET方式搜索

**端点**: `GET /model_provider/_search`

**查询参数**:

- `size`: 返回结果数量 (可选, 默认10, 最大100)
- `from`: 起始位置 (可选, 默认0)
- `sort`: 排序字段 (可选, 格式: `field:order`, 如`created:desc`)
- `q`: 简单文本搜索 (可选, 在name和description字段中搜索)
- `enabled`: 启用状态过滤 (可选, true/false)
- `builtin`: 内置状态过滤 (可选, true/false)
- `api_type`: API类型过滤 (可选)

**请求示例**:

```
GET /model_provider/_search?size=20&from=0&sort=created:desc&q=deepseek&enabled=true
```

#### 5.2 POST方式搜索

**端点**: `POST /model_provider/_search`

**请求格式**:

```json
{
  "size": 20,
  "from": 0,
  "sort": "created:desc",
  "query": {
    "text": "deepseek",
    "filters": {
      "enabled": true,
      "api_type": "openai"
    }
  }
}
```

#### 5.3 OPTIONS预检请求

**端点**: `OPTIONS /model_provider/_search`

**响应头**:

```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, OPTIONS
Access-Control-Allow-Headers: Content-Type
```

#### 5.4 搜索响应格式

```json
{
  "took": 15,
  "timed_out": false,
  "hits": {
    "total": {
      "value": 2,
      "relation": "eq"
    },
    "hits": [
      {
        "_id": "cvj0hjlath21mqh6jbh0",
        "_source": {
          "id": "cvj0hjlath21mqh6jbh0",
          "created": "2024-01-15T10:30:00Z",
          "updated": "2024-01-15T10:30:00Z",
          "name": "DeepSeek AI",
          "api_type": "openai",
          "base_url": "https://api.deepseek.com/v1",
          "icon": "font_deepseek",
          "models": [
            {
              "name": "deepseek-chat"
            }
          ],
          "enabled": true,
          "builtin": false,
          "description": "DeepSeek AI模型提供商"
        }
      }
    ]
  }
}
```

**搜索功能限制**:

- 仅支持基础文本包含匹配 (LIKE操作)
- 不支持复杂的Elasticsearch查询语法
- 不支持聚合查询
- 不支持高亮显示

**状态码**:

- `200 OK`: 搜索成功
- `400 Bad Request`: 查询参数无效
- `500 Internal Server Error`: 服务器内部错误

## 错误响应格式

### 标准错误响应

```json
{
  "error": {
    "type": "ValidationError",
    "message": "请求参数验证失败",
    "details": [
      {
        "field": "name",
        "message": "名称不能为空"
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/model_provider/"
}
```

### 错误类型

- `ValidationError`: 参数验证错误
- `NotFoundError`: 资源不存在
- `ConflictError`: 资源冲突
- `ForbiddenError`: 操作被禁止
- `InternalError`: 服务器内部错误

## 数据验证规则

### 字段验证

- `name`: 1-100字符，不能为空，必须唯一
- `api_key`: 不能为空
- `api_type`: 必须是预定义值之一 (openai, ollama)
- `base_url`: 必须是有效的URL格式
- `models.name`: 不能为空
- `models.settings.temperature`: 0.0-2.0之间的数值
- `models.settings.top_p`: 0.0-1.0之间的数值
- `models.settings.max_tokens`: 1-32768之间的整数

### 业务规则验证

- 内置提供商保护规则
- 名称唯一性检查
- API类型有效性验证
- 模型配置格式验证

## 性能指标

### 响应时间目标

- 创建操作: < 500ms
- 获取操作: < 200ms
- 更新操作: < 300ms
- 删除操作: < 200ms
- 搜索操作: < 500ms

### 并发支持

- 支持100+并发请求
- 数据库连接池优化
- 缓存机制减少数据库压力

## 兼容性说明

### 与Go版本的兼容性

- API路径完全一致
- 请求/响应格式100%兼容
- 错误码和错误消息一致
- 业务逻辑行为一致

### 功能差异

- **暂不支持**: 复杂的Elasticsearch查询语法
- **暂不支持**: 权限控制和认证
- **简化实现**: 基础搜索功能
- **后续完善**: 高级查询和权限系统

## 测试用例

### 创建提供商测试

```bash
curl -X POST http://localhost:9000/model_provider/ \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Provider",
    "api_key": "test-key",
    "api_type": "openai",
    "base_url": "https://api.test.com/v1",
    "enabled": true,
    "description": "测试提供商"
  }'
```

### 搜索提供商测试

```bash
curl -X GET "http://localhost:9000/model_provider/_search?q=test&size=10"
```

### 更新提供商测试

```bash
curl -X PUT http://localhost:9000/model_provider/{id} \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": false,
    "description": "已禁用的测试提供商"
  }'
```
