# 模型提供商API需求规格

## 执行摘要

本项目旨在为Rust重写的Coco服务器实现模型提供商管理API，确保与原Go版本完全兼容。模型提供商API允许用户管理各种AI模型提供商（如OpenAI、DeepSeek、Ollama等），包括创建、查询、更新、删除和搜索功能。

## 利益相关者

### 主要用户
- **系统管理员**: 需要配置和管理模型提供商
- **开发者**: 通过API集成模型提供商功能
- **最终用户**: 通过Web界面管理模型提供商

### 次要用户
- **AI应用开发者**: 使用配置的模型提供商进行AI对话
- **运维人员**: 监控和维护模型提供商服务

### 系统管理员
- **描述**: 负责系统配置和用户权限管理
- **需求**: 完整的CRUD权限，批量操作能力

## 功能性需求

### FR-001: 创建模型提供商
**描述**: 用户可以创建新的模型提供商配置
**优先级**: 高
**验收标准**:
- [ ] 支持POST /model_provider/端点
- [ ] 验证必填字段：name, api_type, base_url
- [ ] 自动生成ID和时间戳
- [ ] 自动设置builtin=false
- [ ] 检查名称唯一性
- [ ] 返回标准创建响应格式

### FR-002: 获取单个模型提供商
**描述**: 根据ID获取特定模型提供商的详细信息
**优先级**: 高
**验收标准**:
- [ ] 支持GET /model_provider/:id端点
- [ ] 返回完整的模型提供商信息
- [ ] 处理不存在的ID（404错误）
- [ ] 过滤敏感信息（api_key）

### FR-003: 更新模型提供商
**描述**: 更新现有模型提供商的配置信息
**优先级**: 高
**验收标准**:
- [ ] 支持PUT /model_provider/:id端点
- [ ] 保护内置提供商的名称不被修改
- [ ] 保护builtin和created字段
- [ ] 验证名称唯一性（如果名称发生变化）
- [ ] 更新updated时间戳

### FR-004: 删除模型提供商
**描述**: 删除指定的模型提供商
**优先级**: 高
**验收标准**:
- [ ] 支持DELETE /model_provider/:id端点
- [ ] 禁止删除内置提供商
- [ ] 返回标准删除响应格式
- [ ] 处理不存在的ID

### FR-005: 搜索模型提供商
**描述**: 支持查询和搜索模型提供商列表（使用SurrealDB基础查询能力）
**优先级**: 高
**验收标准**:
- [ ] 支持GET /model_provider/_search端点
- [ ] 支持POST /model_provider/_search端点
- [ ] 支持OPTIONS /model_provider/_search端点（CORS预检）
- [ ] 支持分页参数（size, from）
- [ ] 支持排序参数（sort）
- [ ] 支持基础过滤条件（enabled, builtin等精确匹配）
- [ ] 支持简单文本搜索（name, description字段包含匹配）
- [ ] 过滤敏感字段（api_key, config）
- [ ] 返回与客户端兼容的响应格式
- [ ] **注意**: 复杂的Elasticsearch查询语法暂不支持，待后续完善

### FR-006: 内置提供商保护
**描述**: 保护系统内置的模型提供商不被误操作
**优先级**: 高
**验收标准**:
- [ ] 内置提供商不能被删除
- [ ] 内置提供商的名称不能被修改
- [ ] 内置提供商的builtin字段不能被修改
- [ ] 其他字段可以正常更新

### FR-007: 模型配置管理
**描述**: 管理每个提供商支持的模型及其配置
**优先级**: 中
**验收标准**:
- [ ] 支持models数组字段
- [ ] 支持模型设置（temperature, top_p等）
- [ ] 验证模型配置格式
- [ ] 支持动态添加/删除模型

### FR-008: 缓存机制
**描述**: 实现模型提供商数据的缓存机制以提高性能
**优先级**: 中
**验收标准**:
- [ ] 实现30分钟TTL的缓存机制
- [ ] 缓存键格式：model_provider:{id}
- [ ] 创建/更新/删除操作自动清除缓存
- [ ] 支持缓存预热和失效策略

### FR-009: 响应格式标准化
**描述**: 确保API响应格式与Go版本完全兼容
**优先级**: 高
**验收标准**:
- [ ] 创建响应：{"_id": "xxx", "result": "created"}
- [ ] 更新响应：{"_id": "xxx", "result": "updated"}
- [ ] 删除响应：{"_id": "xxx", "result": "deleted"}
- [ ] 获取响应：{"_id": "xxx", "found": true, "_source": {...}}
- [ ] 搜索响应：Elasticsearch兼容格式（hits, total等）
- [ ] 错误响应：标准HTTP状态码和错误信息

## 非功能性需求

### NFR-001: 性能
**描述**: API响应时间和吞吐量要求
**指标**:
- API响应时间第95百分位 < 200毫秒
- 支持并发请求数 > 100
- 数据库查询时间 < 50毫秒

### NFR-002: 安全性
**描述**: 认证、授权和数据保护要求
**标准**:
- API密钥等敏感信息加密存储
- 响应中过滤敏感字段
- **注意**: 权限控制暂不实现，待后期完善

### NFR-003: 兼容性
**描述**: 与原Go版本API的兼容性要求
**标准**:
- API路径完全一致
- 服务器-客户端间的请求/响应格式完全一致
- 服务器-客户端间的错误码和错误消息一致
- 客户端无需修改即可使用
- **注意**: 服务器-数据库间的格式不要求与Elasticsearch一致

### NFR-004: 可靠性
**描述**: 系统稳定性和错误处理要求
**标准**:
- 99.9%可用性
- 优雅的错误处理
- 数据一致性保证
- 事务支持

## 约束条件

### 技术约束
- 使用Rust语言和Axum框架
- 使用SurrealDB替代Elasticsearch
- 保持与Go版本API完全兼容
- 遵循现有项目架构模式

### 业务约束
- 不能破坏现有客户端功能
- 必须支持现有的内置提供商
- 保持向后兼容性

### 法规要求
- 遵循数据保护法规
- API密钥安全存储
- 审计日志记录

## 假设条件

- SurrealDB能够提供与Elasticsearch相似的查询能力
- 现有的认证和权限系统可以复用
- 客户端应用不会同时连接Go和Rust版本

## 范围外

- 模型提供商的实际AI模型调用功能
- 模型提供商的健康检查和监控
- 批量导入/导出功能
- 模型提供商使用统计分析
- 复杂的Elasticsearch查询语法支持（待后续完善）
- 完整的权限控制系统（待后续完善）

## 实现说明

### SurrealDB功能限制
- **支持的查询**: 基础的CRUD操作、简单过滤、分页、排序
- **支持的搜索**: 简单的文本包含匹配（LIKE操作）
- **不支持**: 复杂的全文搜索、聚合查询、复杂的查询语法
- **后续计划**: 根据实际需求逐步增强查询能力

### 权限控制简化
- **当前实现**: 暂不实现权限检查，所有API开放访问
- **安全考虑**: 仅在开发和测试环境使用
- **后续计划**: 集成完整的权限控制系统

### 响应格式分层
- **客户端API**: 严格遵循Go版本的响应格式
- **内部数据**: 使用SurrealDB原生格式，不强制Elasticsearch兼容
- **转换层**: 在Handler层进行格式转换
