# 模型提供商API系统架构设计

## 架构概述

本文档定义了模型提供商API的系统架构，采用分层架构模式，确保与Go版本API的完全兼容性，同时利用SurrealDB的优势提供高性能的数据访问。

## 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    客户端层 (Client Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  coco-app (Tauri)  │  Web Browser  │  API Clients  │  CLI   │
└─────────────────────────────────────────────────────────────┘
                                │
                         HTTP/HTTPS
                                │
┌─────────────────────────────────────────────────────────────┐
│                   API网关层 (API Gateway)                    │
├─────────────────────────────────────────────────────────────┤
│              Axum Router + CORS + Middleware               │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  处理层 (Handler Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  ModelProviderHandler  │  ResponseFormatter  │  ErrorHandler │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  服务层 (Service Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  ModelProviderService  │  ValidationService  │  CacheManager │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 仓储层 (Repository Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  ModelProviderRepository  │  QueryBuilder  │  DataMapper    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  数据层 (Data Layer)                        │
├─────────────────────────────────────────────────────────────┤
│                        SurrealDB                           │
└─────────────────────────────────────────────────────────────┘
```

## 分层架构详细设计

### 1. API网关层 (API Gateway)

**职责**：
- HTTP请求路由
- CORS处理
- 中间件管理
- 请求/响应日志

**技术实现**：
```rust
// 路由配置
Router::new()
    .route("/model_provider/", post(create_handler))
    .route("/model_provider/:id", get(get_handler))
    .route("/model_provider/:id", put(update_handler))
    .route("/model_provider/:id", delete(delete_handler))
    .route("/model_provider/_search", get(search_get_handler))
    .route("/model_provider/_search", post(search_post_handler))
    .route("/model_provider/_search", options(search_options_handler))
    .layer(cors_layer)
    .layer(logging_middleware)
```

### 2. 处理层 (Handler Layer)

**职责**：
- HTTP请求解析
- 参数验证
- 响应格式转换
- 错误处理

**核心组件**：

#### ModelProviderHandler
```rust
pub struct ModelProviderHandler {
    service: Arc<ModelProviderService>,
    formatter: Arc<ResponseFormatter>,
}

impl ModelProviderHandler {
    pub async fn create(&self, req: CreateRequest) -> Result<Response, ApiError>
    pub async fn get(&self, id: String) -> Result<Response, ApiError>
    pub async fn update(&self, id: String, req: UpdateRequest) -> Result<Response, ApiError>
    pub async fn delete(&self, id: String) -> Result<Response, ApiError>
    pub async fn search(&self, params: SearchParams) -> Result<Response, ApiError>
}
```

#### ResponseFormatter
```rust
pub struct ResponseFormatter;

impl ResponseFormatter {
    // 转换为Go版本兼容的响应格式
    pub fn format_create_response(id: &str) -> serde_json::Value
    pub fn format_get_response(id: &str, data: &ModelProvider) -> serde_json::Value
    pub fn format_update_response(id: &str) -> serde_json::Value
    pub fn format_delete_response(id: &str) -> serde_json::Value
    pub fn format_search_response(results: &SearchResult) -> serde_json::Value
}
```

### 3. 服务层 (Service Layer)

**职责**：
- 业务逻辑实现
- 数据验证
- 缓存管理
- 事务协调

**核心组件**：

#### ModelProviderService
```rust
pub struct ModelProviderService {
    repository: Arc<dyn ModelProviderRepository>,
    cache: Arc<CacheManager>,
    validator: Arc<ValidationService>,
}

impl ModelProviderService {
    pub async fn create(&self, req: CreateModelProviderRequest) -> Result<String, ServiceError>
    pub async fn get_by_id(&self, id: &str) -> Result<Option<ModelProvider>, ServiceError>
    pub async fn update(&self, id: &str, req: UpdateModelProviderRequest) -> Result<(), ServiceError>
    pub async fn delete(&self, id: &str) -> Result<(), ServiceError>
    pub async fn search(&self, params: SearchParams) -> Result<SearchResult, ServiceError>
    
    // 业务逻辑方法
    async fn validate_builtin_protection(&self, provider: &ModelProvider) -> Result<(), ServiceError>
    async fn check_name_uniqueness(&self, name: &str, exclude_id: Option<&str>) -> Result<(), ServiceError>
    async fn invalidate_cache(&self, id: &str)
}
```

#### CacheManager
```rust
pub struct CacheManager {
    cache: Arc<RwLock<HashMap<String, CacheEntry>>>,
}

pub struct CacheEntry {
    data: ModelProvider,
    expires_at: DateTime<Utc>,
}

impl CacheManager {
    pub async fn get(&self, key: &str) -> Option<ModelProvider>
    pub async fn set(&self, key: &str, value: ModelProvider, ttl: Duration)
    pub async fn invalidate(&self, key: &str)
    pub async fn cleanup_expired(&self) // 定期清理过期缓存
}
```

### 4. 仓储层 (Repository Layer)

**职责**：
- 数据访问抽象
- 查询构建
- 数据映射
- 事务管理

**核心组件**：

#### ModelProviderRepository (Trait)
```rust
#[async_trait]
pub trait ModelProviderRepository: Send + Sync {
    async fn create(&self, provider: &ModelProvider) -> Result<String, RepositoryError>;
    async fn get_by_id(&self, id: &str) -> Result<Option<ModelProvider>, RepositoryError>;
    async fn update(&self, provider: &ModelProvider) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &str) -> Result<(), RepositoryError>;
    async fn search(&self, query: &SearchQuery) -> Result<SearchResult, RepositoryError>;
    async fn find_by_name(&self, name: &str) -> Result<Option<ModelProvider>, RepositoryError>;
    async fn exists(&self, id: &str) -> Result<bool, RepositoryError>;
}
```

#### SurrealModelProviderRepository
```rust
pub struct SurrealModelProviderRepository {
    db: Arc<Surreal<Client>>,
    query_builder: Arc<QueryBuilder>,
}

impl ModelProviderRepository for SurrealModelProviderRepository {
    // 实现所有trait方法
}
```

#### QueryBuilder
```rust
pub struct QueryBuilder;

impl QueryBuilder {
    pub fn build_search_query(&self, params: &SearchParams) -> String {
        // 构建SurrealDB查询语句
        // 支持：分页、排序、基础过滤、简单文本搜索
    }
    
    pub fn build_filter_conditions(&self, filters: &HashMap<String, Value>) -> String
    pub fn build_sort_clause(&self, sort: &str) -> String
    pub fn build_pagination_clause(&self, size: usize, from: usize) -> String
}
```

## 数据模型设计

### 核心数据结构

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelProvider {
    pub id: String,
    pub created: DateTime<Utc>,
    pub updated: DateTime<Utc>,
    pub name: String,
    pub api_key: String,
    pub api_type: String,
    pub base_url: String,
    pub icon: String,
    pub models: Vec<ModelConfig>,
    pub enabled: bool,
    pub builtin: bool,
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfig {
    pub name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub settings: Option<ModelSettings>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelSettings {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub temperature: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub top_p: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub presence_penalty: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub frequency_penalty: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_tokens: Option<u32>,
}
```

### 搜索相关结构

```rust
#[derive(Debug, Deserialize)]
pub struct SearchParams {
    #[serde(default = "default_size")]
    pub size: usize,
    #[serde(default)]
    pub from: usize,
    #[serde(default)]
    pub sort: String,
    #[serde(default)]
    pub q: String, // 简单文本搜索
    #[serde(flatten)]
    pub filters: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Serialize)]
pub struct SearchResult {
    pub hits: Vec<ModelProvider>,
    pub total: usize,
    pub took: u64, // 查询耗时(毫秒)
}
```

## 错误处理架构

### 错误类型层次

```rust
#[derive(Debug, thiserror::Error)]
pub enum ApiError {
    #[error("Service error: {0}")]
    Service(#[from] ServiceError),
    #[error("Validation error: {0}")]
    Validation(String),
    #[error("Not found: {0}")]
    NotFound(String),
}

#[derive(Debug, thiserror::Error)]
pub enum ServiceError {
    #[error("Repository error: {0}")]
    Repository(#[from] RepositoryError),
    #[error("Business logic error: {0}")]
    BusinessLogic(String),
    #[error("Cache error: {0}")]
    Cache(String),
}

#[derive(Debug, thiserror::Error)]
pub enum RepositoryError {
    #[error("Database error: {0}")]
    Database(String),
    #[error("Query error: {0}")]
    Query(String),
    #[error("Serialization error: {0}")]
    Serialization(String),
}
```

## 性能优化策略

### 1. 缓存策略
- **L1缓存**: 内存HashMap，30分钟TTL
- **缓存键**: `model_provider:{id}`
- **缓存失效**: 创建/更新/删除时自动清除
- **缓存预热**: 启动时加载热点数据

### 2. 数据库优化
- **索引策略**: name字段唯一索引，enabled字段普通索引
- **查询优化**: 使用SurrealDB的原生查询能力
- **连接池**: 配置合适的连接池大小

### 3. 并发处理
- **异步处理**: 全链路异步，避免阻塞
- **读写分离**: 读操作优先使用缓存
- **批量操作**: 支持批量查询优化

## 安全考虑

### 1. 数据保护
- **敏感字段过滤**: 响应中自动过滤api_key等敏感信息
- **输入验证**: 严格的参数验证和清理
- **SQL注入防护**: 使用参数化查询

### 2. 访问控制
- **当前阶段**: 暂不实现权限控制
- **预留接口**: 为后续权限集成预留扩展点
- **审计日志**: 记录所有操作日志

## 部署架构

### 1. 服务部署
- **容器化**: Docker容器部署
- **负载均衡**: 支持多实例部署
- **健康检查**: 提供健康检查端点

### 2. 数据库部署
- **SurrealDB**: 独立部署或嵌入式模式
- **备份策略**: 定期数据备份
- **监控**: 数据库性能监控

### 3. 监控和日志
- **应用监控**: 性能指标收集
- **日志聚合**: 结构化日志输出
- **告警机制**: 关键指标告警
